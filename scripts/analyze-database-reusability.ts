#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function analyzeDatabaseReusability() {
  console.log('🔍 分析数据库系统的复用性...');
  
  try {
    // 1. 比较 us_pmn 和 us_class 的 DatabaseConfig 配置
    console.log('\n📋 1. 比较 DatabaseConfig 配置...');
    
    const configs = await db.databaseConfig.findMany({
      where: { 
        code: { in: ['us_pmn', 'us_class'] },
        isActive: true 
      },
      select: {
        code: true,
        name: true,
        category: true,
        accessLevel: true,
        tableName: true,
        modelName: true,
        defaultSort: true,
        maxExportLimit: true,
        defaultExportLimit: true
      }
    });
    
    configs.forEach(config => {
      console.log(`\n  ${config.code}:`);
      console.log(`    名称: ${config.name}`);
      console.log(`    分类: ${config.category}`);
      console.log(`    访问级别: ${config.accessLevel}`);
      console.log(`    表名: ${config.tableName}`);
      console.log(`    模型名: ${config.modelName}`);
      console.log(`    默认排序: ${JSON.stringify(config.defaultSort)}`);
      console.log(`    导出限制: ${config.defaultExportLimit}/${config.maxExportLimit}`);
    });
    
    // 2. 比较 FieldConfig 配置模式
    console.log('\n🔧 2. 比较 FieldConfig 配置模式...');
    
    for (const dbCode of ['us_pmn', 'us_class']) {
      const fieldStats = await db.fieldConfig.groupBy({
        by: ['filterType', 'isVisible', 'isFilterable', 'isSearchable'],
        where: { databaseCode: dbCode, isActive: true },
        _count: true
      });
      
      console.log(`\n  ${dbCode} 字段配置统计:`);
      fieldStats.forEach(stat => {
        console.log(`    ${stat.filterType} | 可见:${stat.isVisible} | 可筛选:${stat.isFilterable} | 可搜索:${stat.isSearchable} - ${stat._count} 个字段`);
      });
    }
    
    // 3. 检查表结构差异
    console.log('\n🗃️ 3. 检查表结构差异...');
    
    // 检查 us_pmn 表是否有 database 字段
    const usPmnColumns = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'us_pmn' 
        AND table_schema = 'public'
      ORDER BY ordinal_position;
    ` as any[];
    
    const usClassColumns = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'us_class' 
        AND table_schema = 'public'
      ORDER BY ordinal_position;
    ` as any[];
    
    console.log(`\n  us_pmn 表字段 (${usPmnColumns.length} 个):`);
    const usPmnHasDatabase = usPmnColumns.some(col => col.column_name === 'database');
    console.log(`    包含 database 字段: ${usPmnHasDatabase ? '是' : '否'}`);
    
    console.log(`\n  us_class 表字段 (${usClassColumns.length} 个):`);
    const usClassHasDatabase = usClassColumns.some(col => col.column_name === 'database');
    console.log(`    包含 database 字段: ${usClassHasDatabase ? '是' : '否'}`);
    
    // 4. 分析 API 路由的通用性
    console.log('\n🌐 4. 分析 API 路由的通用性...');
    
    // 测试两个数据库的 meta API
    for (const dbCode of ['us_pmn', 'us_class']) {
      try {
        const response = await fetch(`http://localhost:3001/api/meta/${dbCode}`);
        const result = await response.json();
        
        if (result.success) {
          const fieldCount = Object.keys(result.data || {}).length;
          const withCountsCount = Object.keys(result.dataWithCounts || {}).length;
          console.log(`  ${dbCode} meta API:`);
          console.log(`    成功: 是`);
          console.log(`    筛选字段数: ${fieldCount}`);
          console.log(`    带计数字段数: ${withCountsCount}`);
        } else {
          console.log(`  ${dbCode} meta API: 失败 - ${result.error}`);
        }
      } catch (error) {
        console.log(`  ${dbCode} meta API: 请求失败 - ${error instanceof Error ? error.message : String(error)}`);
      }
    }
    
    // 5. 分析复用性问题
    console.log('\n🎯 5. 复用性分析结果...');
    
    console.log('\n✅ 当前系统的优势:');
    console.log('  - DatabaseConfig 表统一管理数据库配置');
    console.log('  - FieldConfig 表统一管理字段配置');
    console.log('  - 动态模型映射支持不同表结构');
    console.log('  - API 路由完全通用化 ([database] 动态路由)');
    console.log('  - 前端组件完全配置驱动');
    
    console.log('\n❌ 发现的问题:');
    console.log('  - 部分旧代码仍然假设存在 database 字段');
    console.log('  - 不同表的字段结构不一致导致查询逻辑复杂');
    console.log('  - 缺少标准化的数据库创建模板');
    
    console.log('\n💡 理想状态评估:');
    console.log('  ✅ 新建数据库只需配置 DatabaseConfig + FieldConfig');
    console.log('  ✅ 所有功能(列表、筛选、搜索、导出、统计)自动可用');
    console.log('  ✅ 前端界面完全配置驱动，无需修改代码');
    console.log('  ⚠️  需要清理遗留的 database 字段依赖');
    console.log('  ⚠️  需要标准化表结构设计规范');
    
    console.log('\n🚀 建议改进:');
    console.log('  1. 彻底移除所有 database 字段依赖');
    console.log('  2. 创建标准化的数据库配置模板');
    console.log('  3. 建立数据库创建的最佳实践文档');
    console.log('  4. 实现一键复制配置功能');
    
  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

analyzeDatabaseReusability().catch(console.error);
