"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RefreshCw } from "lucide-react";

interface CaptchaProps {
  value: string;
  onChange: (value: string) => void;
  onValidate: (isValid: boolean) => void;
  onReset?: () => void; // 新增：重置回调，供父组件获取重置方法
  className?: string;
}

export function Captcha({ value, onChange, onValidate, onReset, className = "" }: CaptchaProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [captchaCode, setCaptchaCode] = useState("");
  const [isValidating, setIsValidating] = useState(false);

  // Generate simple, readable captcha code
  const generateCaptchaCode = () => {
    // 使用大写字母和数字，避免混淆字符
    const chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    let result = "";
    for (let i = 0; i < 5; i++) { // 5位数，更容易输入
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // Draw simple, clear captcha
  const drawCaptcha = (code: string) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Simple white background
    ctx.fillStyle = "#ffffff";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw border
    ctx.strokeStyle = "#d1d5db";
    ctx.lineWidth = 1;
    ctx.strokeRect(0, 0, canvas.width, canvas.height);

    // Draw characters clearly and simply
    ctx.font = "bold 24px Arial, sans-serif";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillStyle = "#1f2937";

    // Draw each character with slight spacing
    const charWidth = canvas.width / code.length;
    for (let i = 0; i < code.length; i++) {
      const x = charWidth * i + charWidth / 2;
      const y = canvas.height / 2;

      // Add very slight random position for security (but keep readable)
      const offsetX = (Math.random() - 0.5) * 4;
      const offsetY = (Math.random() - 0.5) * 4;

      ctx.fillText(code[i], x + offsetX, y + offsetY);
    }

    // Add minimal noise for security
    ctx.fillStyle = "#9ca3af";
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      ctx.fillRect(x, y, 1, 1);
    }
  };



  // Simple captcha refresh - only when user clicks or form submits
  const refreshCaptcha = useCallback(() => {
    const newCode = generateCaptchaCode();
    setCaptchaCode(newCode);
    drawCaptcha(newCode);
    onChange("");
    onValidate(false);
  }, [onChange, onValidate]);

  // 将重置方法暴露给父组件
  useEffect(() => {
    if (onReset) {
      onReset(refreshCaptcha);
    }
  }, [onReset, refreshCaptcha]);

  // Simple validation - compare with generated code
  const validateCaptcha = (answer: string): boolean => {
    return answer.toUpperCase() === captchaCode.toUpperCase();
  };

  // Simple validation logic
  useEffect(() => {
    if (value.length === 5 && captchaCode) { // 改为5位
      setIsValidating(true);

      // 简单延迟，模拟验证过程
      const timer = setTimeout(() => {
        const isValid = validateCaptcha(value);
        onValidate(isValid);
        setIsValidating(false);

        // 验证成功后不自动刷新，让用户完成表单提交
        // 刷新由用户手动控制或表单提交后触发
      }, 300);

      return () => clearTimeout(timer);
    } else {
      onValidate(false);
    }
  }, [value, captchaCode, onValidate, refreshCaptcha]);

  // Initialize captcha on mount - only once
  useEffect(() => {
    refreshCaptcha();
  }, []); // 空依赖数组，只在组件挂载时执行一次

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center space-x-3">
        <div className="relative">
          <canvas
            ref={canvasRef}
            width={180}
            height={60}
            className="border border-gray-300 rounded-md bg-white cursor-pointer hover:border-gray-400 transition-colors"
            onClick={refreshCaptcha}
            title="Click to refresh captcha"
          />
          {isValidating && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-md">
              <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
            </div>
          )}
        </div>
        
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={refreshCaptcha}
          className="flex items-center space-x-1"
        >
          <RefreshCw className="h-3 w-3" />
          <span className="text-xs">刷新</span>
        </Button>
      </div>

      <div className="space-y-1">
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value.slice(0, 5).toUpperCase())}
          placeholder="请输入验证码"
          className="w-full text-sm text-center font-mono tracking-wider"
          maxLength={5}
          autoComplete="off"
        />
        <p className="text-xs text-gray-500">
          请输入上方显示的5位验证码（点击图片可刷新）
        </p>
      </div>
    </div>
  );
}

// Alternative: Slider Captcha Component
interface SliderCaptchaProps {
  onValidate: (isValid: boolean) => void;
  className?: string;
}

export function SliderCaptcha({ onValidate, className = "" }: SliderCaptchaProps) {
  const [sliderPosition, setSliderPosition] = useState(0);
  const [targetPosition, setTargetPosition] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  const generateTarget = () => {
    const newTarget = Math.floor(Math.random() * 200) + 50; // 50-250px range
    setTargetPosition(newTarget);
    setSliderPosition(0);
    setIsValidated(false);
    onValidate(false);
  };

  useEffect(() => {
    generateTarget();
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const newPosition = Math.max(0, Math.min(300, e.clientX - rect.left));
    setSliderPosition(newPosition);

    // Check if close to target (within 10px tolerance)
    const tolerance = 15;
    if (Math.abs(newPosition - targetPosition) < tolerance && !isValidated) {
      setIsValidated(true);
      onValidate(true);
    } else if (isValidated && Math.abs(newPosition - targetPosition) >= tolerance) {
      setIsValidated(false);
      onValidate(false);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, targetPosition, isValidated]);

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="space-y-2">
        <p className="text-sm font-medium text-gray-700">
          Drag the slider to the highlighted position
        </p>
        
        <div
          ref={sliderRef}
          className="relative w-80 h-12 bg-gray-200 rounded-md border border-gray-300 overflow-hidden"
        >
          {/* Target zone */}
          <div
            className="absolute top-0 bottom-0 w-8 bg-blue-200 border-2 border-blue-400 rounded"
            style={{ left: `${targetPosition}px` }}
          />
          
          {/* Slider track */}
          <div
            className="absolute top-0 bottom-0 bg-blue-500 rounded-l"
            style={{ width: `${sliderPosition}px` }}
          />
          
          {/* Slider handle */}
          <div
            className={`absolute top-1 bottom-1 w-8 bg-white border-2 rounded cursor-pointer shadow-md transition-colors ${
              isValidated ? 'border-green-500 bg-green-50' : 'border-gray-400'
            } ${isDragging ? 'border-blue-500' : ''}`}
            style={{ left: `${Math.max(0, sliderPosition - 16)}px` }}
            onMouseDown={handleMouseDown}
          >
            <div className="flex items-center justify-center h-full">
              <div className="w-1 h-4 bg-gray-400 rounded"></div>
              <div className="w-1 h-4 bg-gray-400 rounded ml-1"></div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <p className="text-xs text-gray-500">
            {isValidated ? "✓ Verification successful" : "Drag the slider to match the target"}
          </p>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={generateTarget}
            className="text-xs"
          >
            Reset
          </Button>
        </div>
      </div>
    </div>
  );
}
