import { NextRequest, NextResponse } from 'next/server';
// Removed canvas import - now using frontend-only approach

// Store captcha sessions in memory (in production, use Redis)
const captchaSessions = new Map<string, { code: string; timestamp: number }>();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes

// Clean expired sessions
setInterval(() => {
  const now = Date.now();
  for (const [sessionId, session] of captchaSessions.entries()) {
    if (now - session.timestamp > SESSION_TIMEOUT) {
      captchaSessions.delete(sessionId);
    }
  }
}, 5 * 60 * 1000); // Clean every 5 minutes

function generateSessionId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

function generateCaptchaCode(): string {
  // Exclude confusing characters: 0, O, 1, l, I
  const chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
  let result = "";
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Generate checksum for additional security
function generateChecksum(sessionId: string, code: string): string {
  const data = sessionId + code + Date.now().toString();
  // Simple hash function (in production, use crypto.createHash)
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

// Rate limiting storage (in production, use Redis)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const MAX_ATTEMPTS = 10; // Max 10 attempts per minute per IP

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(ip);

  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (record.count >= MAX_ATTEMPTS) {
    return false;
  }

  record.count++;
  return true;
}

function generateMathCaptcha(): { question: string; answer: number } {
  const operations = [
    () => {
      const a = Math.floor(Math.random() * 20) + 1;
      const b = Math.floor(Math.random() * 20) + 1;
      return { question: `${a} + ${b}`, answer: a + b };
    },
    () => {
      const a = Math.floor(Math.random() * 30) + 10;
      const b = Math.floor(Math.random() * 10) + 1;
      return { question: `${a} - ${b}`, answer: a - b };
    },
    () => {
      const a = Math.floor(Math.random() * 12) + 1;
      const b = Math.floor(Math.random() * 12) + 1;
      return { question: `${a} × ${b}`, answer: a * b };
    },
    () => {
      const b = Math.floor(Math.random() * 9) + 2;
      const answer = Math.floor(Math.random() * 15) + 1;
      const a = b * answer;
      return { question: `${a} ÷ ${b}`, answer };
    }
  ];
  
  return operations[Math.floor(Math.random() * operations.length)]();
}

function generateLogicCaptcha(): { question: string; answer: string } {
  const questions = [
    {
      question: "What comes next in the sequence: 2, 4, 6, 8, ?",
      answer: "10"
    },
    {
      question: "If today is Monday, what day will it be in 3 days?",
      answer: "thursday"
    },
    {
      question: "How many letters are in the word 'COMPUTER'?",
      answer: "8"
    },
    {
      question: "What is the opposite of 'hot'?",
      answer: "cold"
    },
    {
      question: "Complete: cat, dog, bird, ?",
      answer: "fish"
    },
    {
      question: "What color do you get when you mix red and blue?",
      answer: "purple"
    },
    {
      question: "How many sides does a triangle have?",
      answer: "3"
    },
    {
      question: "What is 2 to the power of 3?",
      answer: "8"
    }
  ];
  
  return questions[Math.floor(Math.random() * questions.length)];
}

// Generate secure captcha challenge for frontend rendering
function generateSecureCaptchaChallenge(code: string): {
  encryptedChallenge: string;
  config: {
    width: number;
    height: number;
    fontSize: number;
    colors: string[];
    noiseLevel: number;
    interferenceLines: number;
    distortionLevel: number;
    rotationRange: number;
  };
} {
  // Create a complex challenge that includes the code but obfuscated
  const timestamp = Date.now();
  const randomSalt = Math.random().toString(36).substring(2);

  // Create an encrypted challenge that contains rendering instructions
  // but not the actual answer
  const challenge = {
    chars: code.split('').map((char, index) => ({
      char: char,
      x: 20 + index * 25 + Math.random() * 10 - 5,
      y: 40 + Math.random() * 10 - 5,
      rotation: (Math.random() - 0.5) * 0.6,
      color: Math.floor(Math.random() * 6),
      fontSize: 28 + Math.random() * 8 - 4,
    })),
    noise: Array.from({length: 80}, () => ({
      x: Math.random() * 200,
      y: Math.random() * 80,
      size: Math.random() * 3,
      opacity: Math.random() * 0.5,
    })),
    lines: Array.from({length: 8}, () => ({
      x1: Math.random() * 200,
      y1: Math.random() * 80,
      x2: Math.random() * 200,
      y2: Math.random() * 80,
      width: Math.random() * 2 + 1,
      opacity: Math.random() * 0.4 + 0.2,
    })),
    timestamp,
    salt: randomSalt,
  };

  // Base64 encode the challenge (basic obfuscation)
  const encryptedChallenge = Buffer.from(JSON.stringify(challenge)).toString('base64');

  return {
    encryptedChallenge,
    config: {
      width: 200,
      height: 80,
      fontSize: 32,
      colors: ['#2563eb', '#dc2626', '#059669', '#7c2d12', '#4338ca', '#be185d'],
      noiseLevel: 100,
      interferenceLines: 8,
      distortionLevel: 3,
      rotationRange: 0.6,
    }
  };
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type') || 'text';
  const sessionId = generateSessionId();

  // Rate limiting check
  const clientIP = request.headers.get('x-forwarded-for') ||
                   request.headers.get('x-real-ip') ||
                   'unknown';

  if (!checkRateLimit(clientIP)) {
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { status: 429 }
    );
  }

  try {
    if (type === 'image') {
      const code = generateCaptchaCode();
      captchaSessions.set(sessionId, { code, timestamp: Date.now() });

      // Return secure challenge configuration for frontend rendering
      const challenge = generateSecureCaptchaChallenge(code);

      return NextResponse.json({
        type: 'image',
        sessionId,
        challenge: {
          config: challenge.config,
          // Send encrypted rendering instructions (not the actual answer)
          data: challenge.encryptedChallenge,
          // Add additional security measures
          timestamp: Date.now(),
          checksum: generateChecksum(sessionId, code),
        },
      });
    } else if (type === 'math') {
      const { question, answer } = generateMathCaptcha();
      captchaSessions.set(sessionId, { code: answer.toString(), timestamp: Date.now() });
      
      return NextResponse.json({
        type: 'math',
        sessionId,
        question: `${question} = ?`,
      });
    } else if (type === 'logic') {
      const { question, answer } = generateLogicCaptcha();
      captchaSessions.set(sessionId, { code: answer.toLowerCase(), timestamp: Date.now() });
      
      return NextResponse.json({
        type: 'logic',
        sessionId,
        question,
      });
    } else {
      // Default text captcha
      const code = generateCaptchaCode();
      captchaSessions.set(sessionId, { code, timestamp: Date.now() });
      
      return NextResponse.json({
        type: 'text',
        sessionId,
        question: `Enter this code: ${code}`,
      });
    }
  } catch (error) {
    console.error('Captcha generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate captcha' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // Rate limiting check for validation attempts
  const clientIP = request.headers.get('x-forwarded-for') ||
                   request.headers.get('x-real-ip') ||
                   'unknown';

  if (!checkRateLimit(clientIP)) {
    return NextResponse.json(
      { error: 'Too many validation attempts. Please try again later.' },
      { status: 429 }
    );
  }

  try {
    const { sessionId, answer, timestamp, checksum } = await request.json();

    if (!sessionId || !answer) {
      return NextResponse.json(
        { error: 'Session ID and answer are required' },
        { status: 400 }
      );
    }

    // Additional security: check request timing (prevent automated rapid-fire attempts)
    if (timestamp) {
      const requestTime = Date.now() - timestamp;
      if (requestTime < 1000) { // Must take at least 1 second
        return NextResponse.json(
          { error: 'Request too fast. Please try again.' },
          { status: 400 }
        );
      }
      if (requestTime > 300000) { // Must be within 5 minutes
        return NextResponse.json(
          { error: 'Request expired. Please refresh the captcha.' },
          { status: 400 }
        );
      }
    }

    const session = captchaSessions.get(sessionId);
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid or expired captcha session' },
        { status: 400 }
      );
    }

    // Check if session is expired
    if (Date.now() - session.timestamp > SESSION_TIMEOUT) {
      captchaSessions.delete(sessionId);
      return NextResponse.json(
        { error: 'Captcha session expired' },
        { status: 400 }
      );
    }

    // Validate answer (case-insensitive)
    const isValid = answer.toLowerCase().trim() === session.code.toLowerCase();
    
    if (isValid) {
      // Remove session after successful validation
      captchaSessions.delete(sessionId);
    }

    return NextResponse.json({
      valid: isValid,
      message: isValid ? 'Captcha verified successfully' : 'Incorrect answer'
    });

  } catch (error) {
    console.error('Captcha validation error:', error);
    return NextResponse.json(
      { error: 'Failed to validate captcha' },
      { status: 500 }
    );
  }
}
